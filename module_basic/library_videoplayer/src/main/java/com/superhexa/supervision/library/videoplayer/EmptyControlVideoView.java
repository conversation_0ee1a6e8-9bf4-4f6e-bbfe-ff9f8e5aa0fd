package com.superhexa.supervision.library.videoplayer;

import static tv.danmaku.ijk.media.player.IMediaPlayer.MEDIA_ERROR_UNKNOWN;
import static tv.danmaku.ijk.media.player.IMediaPlayer.MEDIA_ERROR_IO;
import static tv.danmaku.ijk.media.player.IMediaPlayer.MEDIA_ERROR_MALFORMED;
import static tv.danmaku.ijk.media.player.IMediaPlayer.MEDIA_ERROR_NOT_VALID_FOR_PROGRESSIVE_PLAYBACK;
import static tv.danmaku.ijk.media.player.IMediaPlayer.MEDIA_ERROR_SERVER_DIED;
import static tv.danmaku.ijk.media.player.IMediaPlayer.MEDIA_ERROR_TIMED_OUT;
import static tv.danmaku.ijk.media.player.IMediaPlayer.MEDIA_ERROR_UNSUPPORTED;

import android.app.Activity;
import android.content.Context;
import android.media.AudioManager;
import android.os.Handler;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.view.WindowManager;

import com.shuyu.gsyvideoplayer.GSYVideoManager;
import com.shuyu.gsyvideoplayer.listener.GSYStateUiListener;
import com.shuyu.gsyvideoplayer.utils.Debuger;
import com.shuyu.gsyvideoplayer.video.StandardGSYVideoPlayer;

import timber.log.Timber;


/**
 * 类描述:无任何控制ui的播放, 基于官方的空View 播放界面 进行了一些方法的拓展，方便使用
 * 创建日期:2021/8/31 on 2:40 下午
 * 作者: FengPeng
 */

public class EmptyControlVideoView extends StandardGSYVideoPlayer {

    private final String tag = EmptyControlVideoView.class.getSimpleName();
    public static final int MEDIA_ERROR_FILE_NOT_EXIST = -10000;

    private static Handler handler = new Handler();

    public EmptyControlVideoView(Context context, Boolean fullFlag) {
        super(context, fullFlag);
    }

    public EmptyControlVideoView(Context context) {
        super(context);
    }

    public EmptyControlVideoView(Context context, AttributeSet attrs) {
        super(context, attrs);
    }


    @Override
    public void seekTo(long position) {
//        super.seekTo(position);  源码中限制了position 不能为0 ，所以重写
        try {
            if (getGSYVideoManager() != null && position >= 0) {
                getGSYVideoManager().seekTo(position);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public int getLayoutId() {
        return R.layout.empty_control_video;
    }

    @Override
    protected void touchSurfaceMoveFullLogic(float absDeltaX, float absDeltaY) {
        super.touchSurfaceMoveFullLogic(absDeltaX, absDeltaY);
        //不给触摸快进，如果需要，屏蔽下方代码即可
        mChangePosition = false;

        //不给触摸音量，如果需要，屏蔽下方代码即可
        mChangeVolume = false;

        //不给触摸亮度，如果需要，屏蔽下方代码即可
        mBrightness = false;
    }

    @Override
    protected void touchDoubleUp(MotionEvent e) {
        //super.touchDoubleUp();
        //不需要双击暂停
    }

    public void setMute(boolean mute) {
        //  releaseAllVideo 后
        //  com.shuyu.gsyvideoplayer.GSYVideoBaseManager.MediaHandler.handleMessage
        //  中 会调用handler 去释放，后导致执行setNeedMute(false)

        handler.postDelayed(() -> {
            GSYVideoManager.instance().setNeedMute(mute);
        }, 150);
    }

    public void setStateUiListener(StateUiListener listener) {
        setGSYStateUiListener(new GSYStateUiListener() {
            @Override
            public void onStateChanged(int state) {
                if (listener != null) {
                    listener.onStateChanged(state);
                }
            }
        });
    }

    public void start() {
        requestAudioFocus();
        int currentState = getCurrentState();
        if (currentState == CURRENT_STATE_AUTO_COMPLETE) {
            setStateAndUi(CURRENT_STATE_PLAYING);
            // GSYVideoManager本身没提供重放的方法，改用StandardGSYVideoPlayer的startPlayLogic
            startPlayLogic();
        } else {
            clickStartIcon();
        }
    }

    private void requestAudioFocus() {
        if (mCurrentState == CURRENT_STATE_PAUSE) {
            if (mAudioManager != null && onAudioFocusChangeListener != null) {
                mAudioManager.requestAudioFocus(onAudioFocusChangeListener, AudioManager.STREAM_MUSIC, AudioManager.AUDIOFOCUS_GAIN_TRANSIENT);
            }
        }
    }

    @Override
    public void onAutoCompletion() {
        Timber.d("onAutoCompletion called");
        // 方便收到播放完成的回调，但是不释放资源， 系统默认释放资源
        setStateAndUi(CURRENT_STATE_AUTO_COMPLETE);
        mHadPlay = false;
//        reset();
    }

    @Override
    public void onCompletion() {
        //make me normal first
        Timber.d("onCompletion called");
        setStateAndUi(CURRENT_STATE_NORMAL);
        reset();
    }

    private void reset() {
        mSaveChangeViewTIme = 0;
        mCurrentPosition = 0;

        if (mTextureViewContainer.getChildCount() > 0) {
            mTextureViewContainer.removeAllViews();
        }

        if (!mIfCurrentIsFullscreen) {
            getGSYVideoManager().setListener(null);
            getGSYVideoManager().setLastListener(null);
        }
        getGSYVideoManager().setCurrentVideoHeight(0);
        getGSYVideoManager().setCurrentVideoWidth(0);

        if (mContext instanceof Activity) {
            try {
                ((Activity) mContext).getWindow().clearFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        releaseNetWorkState();

        if (mVideoAllCallBack != null) {
            Debuger.printfLog("onComplete");
            mVideoAllCallBack.onComplete(mOriginUrl, mTitle, this);
        }

        mHadPlay = false;
    }

    @Override
    public void onError(int what, int extra) {
        // 调用父类的 onError 处理默认行为
        super.onError(what, extra);
        Timber.e("%s Error occurred -> what: %d, extra: %d", tag, what, extra);

        // 错误码参照 tv/danmaku/ijk/media/player/IMediaPlayer.java
        // TODO 暂不处理报错，先看错误类型，后面做针对性处理
        switch (what) {
            case MEDIA_ERROR_UNKNOWN:
                Timber.e("%s MEDIA_ERROR_UNKNOWN 未指定的媒体播放器错误", tag);
                break;
            case MEDIA_ERROR_SERVER_DIED:
                Timber.e("%s MEDIA_ERROR_SERVER_DIED Media server挂了，需要释放 MediaPlayer 对象并实例化一个新的对象", tag);
                break;
            case MEDIA_ERROR_NOT_VALID_FOR_PROGRESSIVE_PLAYBACK:
                Timber.e("%s MEDIA_ERROR_NOT_VALID_FOR_PROGRESSIVE_PLAYBACK 该视频是流式传输的，并且其容器不适用于渐进式播放，即视频的索引（例如 moov atom）不在文件的开头", tag);
                break;
            case MEDIA_ERROR_IO:
                Timber.e("%s MEDIA_ERROR_IO 文件或网络相关操作错误", tag);
                break;
            case MEDIA_ERROR_MALFORMED:
                Timber.e("%s MEDIA_ERROR_MALFORMED 比特流不符合相关的编码标准或文件规范", tag);
                break;
            case MEDIA_ERROR_UNSUPPORTED:
                Timber.e("%s MEDIA_ERROR_UNSUPPORTED 比特流符合相关的编码标准或文件规范，但媒体框架不支持该功能", tag);
                break;
            case MEDIA_ERROR_TIMED_OUT:
                Timber.e("%s MEDIA_ERROR_TIMED_OUT 有些操作需要太长时间才能完成，通常超过 3-5 秒", tag);
                break;
            case MEDIA_ERROR_FILE_NOT_EXIST:
                Timber.e("%s MEDIA_ERROR_FILE_NOT_EXIST 文件不存在 %s", tag, mOriginUrl);
                break;
            default:
                Timber.e("%s 其他错误类型, 未在IMediaPlayer中定义, what -> %d", tag, what);
                break;
        }
    }

    @Override
    public void setStateAndUi(int state) {
        super.setStateAndUi(state);
    }

    public void resumeStart() {
        getGSYVideoManager().start();
    }

    public void pause() {
        onVideoPause();
        setStateAndUi(CURRENT_STATE_PAUSE);
    }

    public void stop() {
        if (GSYVideoManager.instance() != null) {
            GSYVideoManager.instance().stop();
        }
    }

    public void releaseAllVideo() {
        handler.removeCallbacksAndMessages(null);
        GSYVideoManager.releaseAllVideos();
    }

    public long getCurrentPosition() {
        return getGSYVideoManager().getCurrentPosition();
    }

    public boolean isPlaying() {
        return getGSYVideoManager().isPlaying();
    }

    @Override
    protected void onLossAudio() {
        Timber.d("onLossAudio called");
        pause();
    }

    public interface StateUiListener {
        void onStateChanged(int state);
    }
}
